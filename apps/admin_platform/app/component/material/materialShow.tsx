'use client'

import { useEffect, useState } from 'react'
import { Material, MaterialQueryParams, queryMaterials, getMaterialTypes } from '@/app/haogu/api/material'
import MaterialPreview from './materialPreview'

const MATERIAL_TYPES = {
  1: '文本',
  2: '文章',
  3: '文件',
  4: '链接',
  5: '海报',
  6: '图片',
  7: '音频',
  8: '视频',
  9: '语音',
  10: '渠道'
}

interface MaterialType {
  type: number
  count: number
}

export default function MaterialShow() {
  const [materials, setMaterials] = useState<Material[]>([])
  const [materialTypes, setMaterialTypes] = useState<MaterialType[]>([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [total, setTotal] = useState(0)
  const [selectedType, setSelectedType] = useState<number | undefined>(undefined)
  const [previewMaterial, setPreviewMaterial] = useState<Material | null>(null)
  const [isPreviewOpen, setIsPreviewOpen] = useState(false)
  const pageSize = 20

  const loadMaterials = async (params: MaterialQueryParams = {}) => {
    setLoading(true)
    try {
      console.log('开始加载素材...', { page: currentPage, pageSize, type: selectedType, ...params })
      const result = await queryMaterials({
        page: currentPage,
        pageSize,
        type: selectedType,
        ...params
      })
      console.log('加载素材成功:', result)
      setMaterials(result.materials)
      setTotalPages(result.totalPages)
      setTotal(result.total)
    } catch (error) {
      console.error('加载素材失败:', error)
      // 显示错误提示
      alert(`加载素材失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setLoading(false)
    }
  }

  const loadMaterialTypes = async () => {
    try {
      console.log('开始加载素材类型...')
      const types = await getMaterialTypes()
      console.log('加载素材类型成功:', types)
      setMaterialTypes(types)
    } catch (error) {
      console.error('加载素材类型失败:', error)
      alert(`加载素材类型失败: ${error instanceof Error ? error.message : '未知错误'}`)
    }
  }

  useEffect(() => {
    loadMaterials({ page: currentPage, type: selectedType })
  }, [currentPage, selectedType])

  useEffect(() => {
    loadMaterialTypes()
  }, [])

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleTypeChange = (type: number | undefined) => {
    setSelectedType(type)
    setCurrentPage(1)
  }


  const getObjectIdDate = (objectId: string) => {
    try {
      if (objectId && objectId.length === 24) {
        const timestamp = parseInt(objectId.substring(0, 8), 16) * 1000
        return new Date(timestamp).toLocaleString('zh-CN')
      }
      return '未知'
    } catch (error) {
      return '未知'
    }
  }

  const handlePreview = (material: Material) => {
    setPreviewMaterial(material)
    setIsPreviewOpen(true)
  }

  const handleClosePreview = () => {
    setIsPreviewOpen(false)
    setPreviewMaterial(null)
  }

  const getPreviewContent = (material: Material) => {
    const contentData = material.data as any
    switch (material.type) {
      case 6: // 图片
        return contentData.link_url ? (
          <img
            src={contentData.link_url}
            alt="预览"
            className="w-16 h-16 object-cover rounded cursor-pointer"
            onClick={() => handlePreview(material)}
          />
        ) : null
      case 8: // 视频
        return contentData.video_img ? (
          <div className="relative cursor-pointer" onClick={() => handlePreview(material)}>
            <img
              src={contentData.video_img}
              alt="视频封面"
              className="w-16 h-16 object-cover rounded"
            />
            <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30 rounded">
              <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M8 5v10l8-5-8-5z"/>
              </svg>
            </div>
          </div>
        ) : null
      default:
        return null
    }
  }

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">素材管理</h1>

      {/* 类型筛选 */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          <button
            className={`btn btn-sm ${selectedType === undefined ? 'btn-primary' : 'btn-outline'}`}
            onClick={() => handleTypeChange(undefined)}
          >
            全部 ({total})
          </button>
          {materialTypes.map((typeInfo) => (
            <button
              key={typeInfo.type}
              className={`btn btn-sm ${selectedType === typeInfo.type ? 'btn-primary' : 'btn-outline'}`}
              onClick={() => handleTypeChange(typeInfo.type)}
            >
              {MATERIAL_TYPES[typeInfo.type as keyof typeof MATERIAL_TYPES] || `类型${typeInfo.type}`} ({typeInfo.count})
            </button>
          ))}
        </div>
      </div>

      {/* 加载状态 */}
      {loading && (
        <div className="flex justify-center py-8">
          <span className="loading loading-spinner loading-lg"></span>
        </div>
      )}

      {/* 素材列表 */}
      {!loading && (
        <div className="overflow-x-auto">
          <table className="table table-zebra w-full">
            <thead>
              <tr>
                <th>预览</th>
                <th>标题</th>
                <th>描述</th>
                <th>类型</th>
                <th>上传时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody>
              {materials.map((material) => (
                <tr key={material.id}>
                  <td>
                    <div className="w-16 h-16 flex items-center justify-center">
                      {getPreviewContent(material) || (
                        <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center cursor-pointer"
                          onClick={() => handlePreview(material)}>
                          <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className="font-medium cursor-pointer hover:text-primary" onClick={() => handlePreview(material)}>
                      {material.title || '无标题'}
                    </div>
                  </td>
                  <td>
                    <div className="max-w-xs truncate">
                      {material.description || '无描述'}
                    </div>
                  </td>
                  <td>
                    <span className="badge badge-outline">
                      {MATERIAL_TYPES[material.type as keyof typeof MATERIAL_TYPES] || `类型${material.type}`}
                    </span>
                  </td>
                  <td>
                    <span className="text-sm text-gray-500">
                      {getObjectIdDate(material.id)}
                    </span>
                  </td>
                  <td>
                    <div className="flex gap-2">
                      <button
                        className="btn btn-xs btn-outline btn-primary"
                        onClick={() => handlePreview(material)}
                      >
                        预览
                      </button>
                      <button className="btn btn-xs btn-outline btn-error">删除</button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {materials.length === 0 && !loading && (
            <div className="text-center py-8 text-gray-500">
              暂无素材数据
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-6">
          <div className="join">
            <button
              className="join-item btn"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              上一页
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i
              return (
                <button
                  key={page}
                  className={`join-item btn ${currentPage === page ? 'btn-active' : ''}`}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </button>
              )
            })}

            <button
              className="join-item btn"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              下一页
            </button>
          </div>
        </div>
      )}

      {/* 分页信息 */}
      {materials.length > 0 && (
        <div className="text-center mt-4 text-sm text-gray-500">
          第 {currentPage} 页，共 {totalPages} 页，总计 {total} 条记录
        </div>
      )}

      {/* 预览模态框 */}
      <MaterialPreview
        material={previewMaterial}
        isOpen={isPreviewOpen}
        onClose={handleClosePreview}
      />
    </div>
  )
}