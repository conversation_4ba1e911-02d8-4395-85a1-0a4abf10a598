'use server'

import { AdminPrismaMongoClient } from '@/lib/prisma'

export interface Material {
  id: string
  source_id: string
  type: number
  title: string
  description: string
  data: any
  created_at?: Date
}

export interface MaterialQueryParams {
  page?: number
  pageSize?: number
  type?: number
}

export async function queryMaterials(params: MaterialQueryParams = {}) {
  const { page = 1, pageSize = 20, type } = params
  const skip = (page - 1) * pageSize

  try {
    const whereCondition: any = {}
    if (type !== undefined) {
      whereCondition.type = type
    }

    const [materials, total] = await Promise.all([
      AdminPrismaMongoClient.getHaoguInstance().material.findMany({
        where: whereCondition,
        skip,
        take: pageSize,
        orderBy: {
          id: 'desc'  // 使用id排序代替created_at
        }
      }),
      AdminPrismaMongoClient.getHaoguInstance().material.count({
        where: whereCondition
      })
    ])

    return {
      materials,
      total,
      page,
      pageSize,
      totalPages: Math.ceil(total / pageSize)
    }
  } catch (error) {
    console.error('Error querying materials:', error)
    throw error
  }
}

export async function getMaterialTypes() {
  try {
    const materials = await AdminPrismaMongoClient.getHaoguInstance().material.groupBy({
      by: ['type'],
      _count: {
        type: true
      }
    })

    return materials.map((item) => ({
      type: item.type,
      count: item._count.type
    }))
  } catch (error) {
    console.error('Error getting material types:', error)
    throw error
  }
}