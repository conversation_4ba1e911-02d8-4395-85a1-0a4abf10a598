import logger from 'model/logger/logger'
import { chatDBClient, chatStateStoreClient } from '../config/instance/base_instance'
import { checkRobotDetection } from 'service/agent/utils'
import { commonMessageSender } from '../config/instance/send_message_instance'
import { DateHelper } from 'lib/date/date'
import { DataService } from './helper/getter/get_data'
import { getPrompt } from 'service/agent/prompt'
import { humanTransferClient } from '../config/instance/instance'
import { IChattingFlag } from '../config/manifest'
import { IWorkflowState } from 'service/llm/state'
import { LLM } from 'lib/ai/llm/llm_model'
import { RegexHelper } from 'lib/regex/regex'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { sleep } from 'lib/schedule/schedule'
import { TaskName } from './schedule_task/register_task'
import { Node } from 'service/agent/workflow'

export class Router {
  /**
   * 根据客户消息进行路由，特别注意这里的路由要写的 特定情况才能跳转，不能太通用，不然容易路由到错误的节点
   * 返回 End, 表示不执行任何节点逻辑
   * @param state
   */
  public static async route(state: IWorkflowState): Promise<Node> {
    const chatId = state.chat_id
    const userId = state.user_id
    const roundId = state.round_id
    const userMessage = state.userMessage
    const currentTime = await DataService.getCurrentTime(chatId)
    const day = currentTime.day
    if (!userMessage) return Node.Dummy

    // 刷新到完课状态
    if (0 < day && day < 5 && DateHelper.isTimeAfter(currentTime.time, '18:50:00')) {
      // await DataService.isAttendCourse(chatId, day)
      await DataService.isCompletedCourse(chatId, day)
    }

    // 废话过滤
    const isChatter = RegexHelper.filterChatter(userMessage)
    const beforeCourse3 = await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 3)
    if (isChatter && beforeCourse3) return Node.DummyEnd

    // 客户识别AI检查
    const isRobotDetection = await checkRobotDetection(chatStateStoreClient, humanTransferClient, chatId, roundId, userId, userMessage)
    if (isRobotDetection) return Node.DummyEnd

    // // 检查是否还在欢迎语阶段
    // const welcomeStageResult = await this.handleWelcomeStage(chatId)
    // if (welcomeStageResult) return Node.DummyEnd

    // 意图分类路由
    return await this.routeByCategory(userMessage, chatId, userId, roundId)
  }

  // /**
  //  * 处理欢迎语阶段的逻辑
  //  */
  // static async handleWelcomeStage(chatId: string): Promise<boolean> {
  //   const state = await chatStateStoreClient.getFlags<IChattingFlag>(chatId)
  //
  //   if (state.is_welcome_stage_completed) {
  //     return false
  //   }
  //   if (!state.is_send_core_materials) {
  //     await this.sendCoreMaterials(chatId)
  //     return true
  //   }
  //   if (!state.is_send_installation_guide) {
  //     await this.sendInstallationGuide(chatId)
  //     return true
  //   }
  //   return false
  // }

  // /**
  //  * 发送筹码峰核心资料
  //  */
  // static async sendCoreMaterials(chatId: string): Promise<void> {
  //   await sleep(5000)
  //
  //   await commonMessageSender.sendText(chatId, {
  //     text: '🎁筹码峰核心资料：\n' +
  //         '【神奇的双线合一（选股）】https://ai.9635.com.cn/pw6wwafF【6分钟】 \n' +
  //         '【绝密的四点共振（买点）】https://ai.9635.com.cn/yjm8T2RA【7分钟】 \n' +
  //         '上面预习视频是教您如何使用无延迟筹码的，您看完跟我说哦，我来教您安装工具&调出筹码峰～',
  //     description: '客户10分钟内回复后发送筹码峰核心资料'
  //   })
  //   await chatStateStoreClient.update(chatId, {
  //     state: <IChattingFlag>{
  //       is_send_core_materials: true
  //     }
  //   })
  //
  //   // 启动安装教程定时任务（5分钟后）
  //   await SilentReAsk.schedule(
  //     TaskName.SendInstallationGuide,
  //     chatId,
  //     5 * 60 * 1000,
  //     5,
  //     { auto_retry: true, independent: true }
  //   )
  //   await SilentReAsk.schedule(
  //     TaskName.SendInstallationGuide,
  //     chatId,
  //     10 * 60 * 1000,
  //     10,
  //     { auto_retry: true, independent: true }
  //   )
  // }
  //
  // /**
  //  * 发送安装教程
  //  */
  // static async sendInstallationGuide(chatId: string): Promise<void> {
  //   await sleep(5000)
  //
  //   await commonMessageSender.sendText(chatId, {
  //     text: '筹码峰如何安装？电脑和手机都可以。看下教程，有不懂的问我。接下来正式开课我也会通知你的，是在微信直播！ 👉视频教程： https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv \n' +
  //         '​\n' +
  //         '手机版、电脑版下载链接： https://download.9635.com.cn/',
  //     description: '客户10分钟内回复后发送安装教程'
  //   })
  //
  //   // 更新状态，标记安装教程已发送和欢迎语阶段完成
  //   await chatStateStoreClient.update(chatId, {
  //     state: <IChattingFlag>{
  //       is_send_installation_guide: true,
  //       is_welcome_stage_completed: true
  //     }
  //   })
  // }

  // 意图分类路由
  private static async routeByCategory(userMessage: string, chat_id: string, user_id: string, round_id: string): Promise<Node> {
    const category = await Router.classify(userMessage, chat_id, round_id)

    if (category === 7) {  // 看课问题
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
      const phoneNumber = await chatDBClient.getPhone(chat_id)
      const message = phoneNumber ? `用这个手机号登录哈，老师帮你开权限了\n${phoneNumber}` : '麻烦提供一下手机号哈，这边后台帮你开权限'
      await commonMessageSender.sendText(chat_id, {
        text: message,
        description: '客户无法看课，给解决方案'
      })
      return Node.DummyEnd
    } else if (category === 8) {  // 异常问题
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, 'onlyNotify', `客户：${userMessage}`)
    } else if (category === 9) {  // 人工处理
      // await HumanTransfer.transfer(chat_id, user_id, HumanTransferType.ProblemSolving, true, `客户：${userMessage}`)
      return Node.DummyEnd
    }
    return Node.Dummy  // 正常对话
  }

  public static async classify(userMessage: string, chat_id: string, round_id: string) {
    const routerPrompt = await getPrompt('free-route')
    const routingNodes = ''
    const output = await LLM.predict(
      routerPrompt, {
        responseJSON: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        routingNodes: routingNodes,
        customerMessage: userMessage,
      })
    let answer: number = 0

    try {
      const parsedOutput = JSON.parse(output)
      answer = parsedOutput.answer
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    return answer || 0
  }
}