import logger from 'model/logger/logger'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../config/instance/base_instance'
import { IChattingFlag } from '../../config/manifest'
import { sleep } from 'openai/core'
import { getUserId } from 'config/chat_id'
import { DataService } from '../helper/getter/get_data'
// import { SalesCase } from '../../helper/rag/sales_case'
import { ActionInfo } from 'service/agent/stage'
import { SendMessageType } from 'service/visualized_sop/common_sender/type'
import { commonMessageSender } from '../../config/instance/send_message_instance'
import { commonSleep } from 'lib/schedule/schedule'
import { LLM } from 'lib/ai/llm/llm_model'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { SilentReAsk } from 'service/schedule/silent_requestion'
import { TaskName } from '../schedule_task/register_task'

export class PostAction {
  public static getPeiPaoCourseIntro(): string {
    return `陪跑营卖点:
- 陪跑营核心是中神通老师带队直接带你30天迈过从知道思路到拿到结果过程中所有的问题
- 整个过程是1对1私人订制您的方案，陪跑过程分为3个阶段。
  - 第一阶段（0-3天）：围绕账号定位账号搭建，根据你规模和你的生意订制你的账号定位。
  - 第二阶段（3-15天）：打造人设，拍摄剪辑。手把手带你用532 内容生产法则，真正打造能获客的账号。
  - 第三阶段（15-30天）：全矩阵的，直播引流，私域运营。
- 除了1对1陪跑服务外，还配套提供以下福利：
  - 365天答疑：30天结束后，咱们的陪跑群还是继续存在，365天给您答疑，平台规则发生的变化，后续落地遇到的任何问题都可以陪伴解答。
  - 37节的获客实操课程，以及团购直播课程：除了私人订制的陪跑服务之外，陪跑营还赠送37节的获客实操课程，以及团购直播课程，这些课程都是中神通老师自己讲的课程，都是非常落地的干货。支持永久回放。
  - AI数字人和AI爆店系统：在整个陪跑过程，针对出镜头难，文案能力不行的问题，赠送AI数字人和AI爆店系统，解决这2个问题。基本去掉了所有做起来账号的技术难点了。要的就是咱一定要做起来的决心了！只要您想做，我们就有信心帮您拿到结果。
  - 3天2夜的线下课：最后重磅！除了线上对接之外，2980还赠送3天2夜的线下课（机票酒店自理），直接现场带您实操落地！`
  }

  static async sendCourseIntro(chat_id: string) {
    const user_id = getUserId(chat_id)

    const actionInfo: ActionInfo = {
      guidance: PostAction.getPeiPaoCourseIntro(),
    }

    if ((await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)).is_send_course_intro) { return actionInfo }

    actionInfo.callback = async () => {
      await sleep(2000)

      await chatStateStoreClient.update(chat_id, { state: <IChattingFlag> { is_send_course_intro: true } })
    }
    return actionInfo
  }

  public static async sendInvitation() {
    const purchaseLink = 'http://3tG7eG1l.w.njqzlive.com/p/IuVJkRcT'
    const actionInfo: ActionInfo = { guidance: `务必向客户发送下单链接：${purchaseLink}，并邀约客户购买` }
    return actionInfo
  }

  // public static async sendCaseImage(chat_id: string): Promise<IActionInfo> {
  //   const user_id = getUserId(chat_id)
  //   const searchResult = await SalesCase.searchByChatId(chat_id, [SalesCase.peipaoyingDoc])
  //   const revenueTmp = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chat_id, '基本信息', '年营业额')
  //   const revenue = revenueTmp === '' ? '20万' : revenueTmp
  //   const actionInfo: IActionInfo = { guidance: `案例信息：学员学习前：${revenue}左右年营业额（营业额数值可以随机一点），学员学习后：营业额有提升但不要太夸张` }
  //
  //   if (!searchResult) { return actionInfo }
  //
  //   const chatHistory = await chatHistoryServiceClient.formatHistoryOnRole(chat_id, 'assistant', 5)
  //
  //   if (chatHistory.includes(`${searchResult.topic}案例1`)) { return actionInfo }
  //
  //   const sendImages = async () => {
  //     await sleep(5000)
  //     const images = searchResult.images.slice(0, 3)
  //
  //     for (let i = 0; i < images.length; i++) {
  //       const image = images[i]
  //       await sleep(1000)
  //
  //       await wecomMessageSender.sendById({
  //         user_id: user_id,
  //         chat_id: chat_id,
  //         ai_msg: `[${searchResult.topic}案例${i + 1}]`,
  //         send_msg: {
  //           type: IWecomMsgType.Image,
  //           url: image,
  //         },
  //       })
  //     }
  //   }
  //
  //   actionInfo.guidance = `案例信息：${searchResult.description}`
  //   actionInfo.callback = sendImages
  //   return actionInfo
  // }

  public static async provideDepositPlan() {
    const actionInfo: ActionInfo = { guidance: '主动提出可以先付1000定金，后续进入陪跑营之后再补齐，现在使用花呗还可以分期付款' }
    return actionInfo
  }

  public static async reaskAnotherDay(chat_id: string) {
    logger.trace({ chat_id: chat_id }, '客户保留名额，次日询问')
    // await SilentReAsk.schedule(TaskName.reask_another_day, chat_id, 24 * 60 * 60 * 1000, {}, { auto_retry: true }) // 24小时后提醒客户
    return { guidance: '' } as ActionInfo
  }

  /**
   * 返回day5 9 13等课程时间的**开课时间**
   * @param chat_id
   */
  public static async getNewCourseStartDayDiff(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    let day = currentTime.day
    // day = 0 为 day 5开课，day = 5为当天开课
    if (day == 0) {
      return 5
    }
    if (day > 1) {
      day = day - 1
    }
    const multiple = Math.ceil(day / 4)
    return  multiple * 4 - currentTime.day + 1
  }

  public static async sendPreviewVideo(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    if (state.is_send_core_materials) {
      logger.log(`筹码峰核心资料已发送，跳过任务 for chat: ${chat_id}`)
      return { guidance: '已经给客户发过预习视频，正常回复客户消息即可', callback: async() => {} }
    }
    await commonMessageSender.sendText(chat_id, {
      text: '🎁筹码峰核心资料：\n' +
          '【神奇的双线合一（选股）】https://ai.9635.com.cn/pw6wwafF【6分钟】 \n' +
          '【绝密的四点共振（买点）】https://ai.9635.com.cn/yjm8T2RA【7分钟】 \n' +
          '上面预习视频是教您如何使用无延迟筹码的，您看完跟我说哦，我来教您安装工具&调出筹码峰～',
      description: '发送预习视频'
    })
    // 更新状态
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        is_send_core_materials: true
      }
    })
    await SilentReAsk.schedule(
      TaskName.SendInstallationGuide,
      chat_id,
      5 * 60 * 1000,
      5,
      { auto_retry: true, independent: false }
    )
    return { guidance: '预习视频已发送，正常回复客户消息即可', callback: async() => {} }
  }

  public static async sendInstallationGuide(chat_id: string): Promise<ActionInfo> {
    const state = await chatStateStoreClient.getFlags<IChattingFlag>(chat_id)

    // 如果已经发送过，则不再发送
    if (state.is_send_installation_guide) {
      logger.log(`安装教程已发送，跳过任务 for chat: ${chat_id}`)
      return { guidance: '已经给客户发过安装教程，正常回复客户消息即可', callback: async() => {} }
    }
    await commonMessageSender.sendText(chat_id, {
      text: '筹码峰如何安装？电脑和手机都可以。看下教程，有不懂的问我。接下来正式开课我也会通知你的，是在微信直播！ 👉视频教程： https://drive.weixin.qq.com/s?k=AGwAqAeQAA0d84u1Sv \n' +
          '​\n' +
          '手机版、电脑版下载链接： https://download.9635.com.cn/',
      description: '发送筹码峰安装教程'
    })
    await chatStateStoreClient.update(chat_id, {
      state: <IChattingFlag>{
        is_send_installation_guide: true,
        is_welcome_stage_completed: true
      }
    })
    return { guidance: '安装教程已发送，正常回复客户消息即可', callback: async() => {} }
  }

  public static async askQuestionOne(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text:'股市如江湖，有人快刀出鞘⚡，有人稳坐钓鱼台🎣。想知道自己现在的江湖段位吗？来个小测试，三组趣味题，1分钟帮你测出你的股市段位🤗',
      description:'开始进行挖需，介绍挖需测评小游戏'
    })
    await commonSleep()
    await commonMessageSender.sendText(chat_id, {
      text: `先回答第一组问题，请直接回复字母，比如：A B C D
1. 您的江湖身份：
A. 男青年（热血上头型）
B. 男中年（拼搏稳健型）
C. 老年退休（沉稳低调型）
D. 女股侠（清醒理智型）

2. 您征战股市多久：
A. 不到1年，刚入江湖
B. 1-3年，有点经验
C. 3-5年，老鸟但浮躁
D. 5年以上，资深沉淀

3. 您师从哪个门派？
A. 独门自创（纯凭感觉）
B. 江湖股吧（跟风信群）
C. 学术研读（书上高手）
D. 各种机构（培训常客）`,
      description: '挖需第一组问题，询问投资身份类型，进入股市时长，投机理念，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askQuestionTwo(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `收到～ 请直接回字母回答接下来一组：

您惯用的炒股招式是：
A. 短线追涨杀跌（快进快出）
B. 中长线持股（耐心等待）
C. 消息面驱动（谁热买谁）
D. 随机操作（靠运气）

您的出手资金一般是多少？
A. 不到5万（新手尝试）
B. 5–30万（实操练级）
C. 30–100万（认真投资）
D. 100万以上（重仓对决）

您过去的炒股战绩是？
A. 稳定盈利（高手风范）
B. 时赚时亏（过山车）
C. 基本持平（佛系观望）
D. 经常亏损（割肉专业户）`,
      description: '挖需第二组问题，询问炒股技巧，炒股资金情况，过往炒股战绩,三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askQuestionThree(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `收到～具体测评结果公布还差最后一步啦：

您最大的交易困扰是？
A. 不会选股，瞎买
B. 不会止损，爱扛单
C. 资金曲线乱，心情也乱
D. 没方法，全靠运气

您更偏好的收益方式？
A. 快进快出，追热点
B. 稳扎稳打，选结构
C. 中间摇摆，无法坚定
D. 想稳住但总忍不住

炒股对你来说是为了？
A. 一夜暴富
B. 赚点零花、补贴生活
C. 稳定理财、退休准备
D. 逆人性修炼自我纪律`,
      description: '挖需第三组问题，询问最大交易困扰，看什么判断买点，偏好的收益方式，炒股理由，三道题，每道题ABCD四个选项，A到D分数逐渐增高，D为最高分'
    }, {
      force:true
    })
    return { guidance: '结束后续回复', callback: async() => {} }
  }

  public static async askResultLevel(chat_id:string, round_id:string):Promise<ActionInfo> {
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        is_finish_stock_ranking_assessment:true
      }
    })
    const chatHistory = await chatHistoryServiceClient.getFormatChatHistoryByChatId(chat_id, true)
    const prompt = SystemMessagePromptTemplate.fromTemplate(`# 你的任务是根据客户的聊天信息判断出客户的具体炒股水平。
请根据情况将用户分为5种等级
- level1:江湖菜鸟（新手试水，无体系，靠感觉）
- level2:小试牛刀（有经验但系统弱，风格不稳）
- level3:江湖老手（认知提升中，缺执行纪律）
- level4:理性剑客（思路清晰，结构意识强，缺落地）
- level5:宗师境界（系统成熟，目标清晰，等待放大）

以下是具体的聊天记录

{chatHistory}

# 输出要求
请严格按照如下 JSON 格式输出
{{
  "think": "（深度思考）",
  "level": "（具体等级，直接输出数字等级）"
}}`)
    const output = await LLM.predict(
      prompt, {
        responseJSON: true,
        meta: {
          promptName: 'router',
          chat_id: chat_id,
          round_id: round_id,
        } }, {
        chatHistory
      })
    let level: number = 3

    try {
      const parsedOutput = JSON.parse(output)
      level = parsedOutput.level
    } catch (error) {
      logger.error('Router 解析 JSON 失败:', error)
    }
    if (level == 1) {
      return await PostAction.askResultLevel1(chat_id, round_id)
    } else if (level == 2) {
      return await PostAction.askResultLevel2(chat_id, round_id)
    } else if (level == 3) {
      return await PostAction.askResultLevel3(chat_id, round_id)
    } else if (level == 4) {
      return await PostAction.askResultLevel4(chat_id, round_id)
    } else if (level == 5) {
      return await PostAction.askResultLevel5(chat_id, round_id)
    }
    await commonMessageSender.sendText(chat_id, {
      text: `段位一｜江湖菜鸟
您的段位是｜江湖菜鸟。您初入股市江湖，胆识过人，敢于下场，像少年侠客般一腔热血。勇气虽足，却多凭直觉出手，缺乏一套完整的心法，容易随风起舞，时而意外得手，时而仓促败退。气势上锐不可当，但心境尚浅，情绪易被市场牵动，出招凌厉却难以稳定。此时的您，如同初生牛犊，激情澎湃，却未能驭势控局。`,
      description: '股票段位评测结束了，你的评测结果是江湖菜鸟'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel1(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位一｜江湖菜鸟
您的段位是｜江湖菜鸟。您初入股市江湖，胆识过人，敢于下场，像少年侠客般一腔热血。勇气虽足，却多凭直觉出手，缺乏一套完整的心法，容易随风起舞，时而意外得手，时而仓促败退。气势上锐不可当，但心境尚浅，情绪易被市场牵动，出招凌厉却难以稳定。此时的您，如同初生牛犊，激情澎湃，却未能驭势控局。`,
      description: '股票段位评测结束了，你的评测结果是江湖菜鸟'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel2(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位二｜小试牛刀
您的段位是｜小试牛刀。您已在江湖中摸爬滚打一番，手中已有几招常用招式，偶尔能斩获战果。经验渐丰，但尚未凝练成稳定的套路，出手间常有摇摆，时而犀利，时而迟疑。气质上既有历练者的稳重，也带着江湖人心浮动的影子。整体来看，您已脱离莽撞，却仍在寻找一条真正适合自己的路，剑意初成，却未至圆融。`,
      description: '股票段位评测结束了，你的评测结果是小试牛刀'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel3(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位三｜江湖老手
您的段位是🐯｜江湖老手。您在股市江湖行走多年，眼界开阔，已能分辨虚实，心中逐渐生出框架。您能洞察行情起落，但在实战中，常因情势突变而动摇初心，计划难以始终如一。您的姿态像久经沙场的侠客，能握剑镇场，却难免被江湖风雨扰乱心神。整体而言，已能立足江湖，但剑意未稳，火候尚欠，仍需打磨心性与定力。`,
      description: '股票段位评测结束了，你的评测结果是江湖老手'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel4(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位四｜理性剑客
您的段位是｜理性剑客。您已能冷眼看市，结构与逻辑在心中逐渐成型，举止间透着从容。思维清晰，懂得分辨虚与实，能从乱象中找准落点。但在具体落地时，偶尔因迟疑错过良机，或在风云突变时未能果断出剑。您的气质如修炼多年的独行剑客，内功浑厚，步伐稳健，却在细节上仍有空隙。此刻的您，离圆满只差临门一脚。`,
      description: '股票段位评测结束了，你的评测结果是理性剑客'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
  public static async askResultLevel5(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendText(chat_id, {
      text: `段位五｜宗师境界
您的段位是｜宗师境界。您已登上股市江湖的高台，逻辑与体系融为一体，进退皆有章法，出剑从容不迫。无论行情起落，皆能保持节奏，展现大将之风。然而，宗师亦有桎梏，长年形成的套路虽稳固，却易固守成规，对新局面的适应未必敏捷。此时的您，如登峰造极的武林高手，已立于群山之巅，但若要再拓天地，还需不断求变。`,
      description: '股票段位评测结束了，你的评测结果是宗师境界'
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }

  public static async sendEconomicCurve(chat_id:string, round_id:string):Promise<ActionInfo> {
    await commonMessageSender.sendMsg(chat_id, [{
      type: SendMessageType.image,
      url:'https://free-spirit-static.oss-cn-hangzhou.aliyuncs.com/haogu/asset/expert_capital_curve.png',
      description:'高手资金曲线图片'
    }, {
      type:SendMessageType.text,
      text:'你看，这就是高手的资金曲线——平稳向上，没有剧烈的大起大落。交易的终局不是靠一次暴富改变命运，而是靠长期稳定复利，让资金曲线越来越漂亮。股市是“久富平台”，不是“暴富平台”。真正爽的交易，是每一次买卖都胸有成竹，利润是努力和学习的自然结果；而不是今天赚明天亏，情绪跟着账户上蹿下跳。你可以对照看下你的投资账户资金曲线，也可一发给我来看看🫣',
      description:'介绍高手资金曲线'
    }, {
      type:SendMessageType.text,
      text:'我们好人好股，是唯一一家把股民当成交易员进行专业赋能的培训机构。我们不推荐股票，而是授人以渔，真心希望教会学员一套稳定赚钱的交易方法，让每一次操作都有章可循。很开心在茫茫人海中遇到你！接下来，我们将开启一段6天的股民开悟之旅，让你彻底搞明白——你在股市赚的是什么钱、如何持续稳定地赚钱。📅 明天第一节课，记得准时来上课，我们直播见！',
      description:'介绍高手资金曲线后介绍好人好股'
    }])
    await chatStateStoreClient.update(chat_id, {
      state:<IChattingFlag>{
        after_bonding:true
      }
    })
    return { guidance: '如果感到需要就简单结尾一下，否则就不说话', callback:async() => {} }
  }
}