import { ContentItem, ContentItemMediaType, ContentItemType } from 'model/haogu/crm/type'
import { haoguScrmApi } from '../../../config/instance/api_instance'
import { PrismaMongoClient } from '../../../database/prisma'

export enum MaterialType {
  Text = 1,
  Article = 2,
  File = 3,
  Link = 4,
  Poster = 5,
  Image = 6,
  Audio = 7,
  Video = 8,
  Voice = 9,
  Channels = 10,
}


export class MaterialManager {
  public async searchMaterialById(sourceId: string) {
    return PrismaMongoClient.getInstance().material.findFirst({
      where: {
        source_id: sourceId
      }
    })
  }

  public async saveMaterial(item: ContentItem) {
    await PrismaMongoClient.getInstance().material.create({
      data: {
        source_id:item.id.toString(),
        type:this.ItemTypeToMediaType(item),
        title:'',
        description:'',
        data:JSON.parse(JSON.stringify(item))
      }
    })
  }

  public async searchMaterialFromHaoGu(type: number, mediaType?: number) {
    const res = await haoguScrmApi.contentList(1, 10, 113, type, mediaType, 0)

    return res
  }

  private ItemTypeToMediaType(item: ContentItem) {
    switch (item.type) {
      case ContentItemType.Article:
        return MaterialType.Article
      case ContentItemType.File:
        return MaterialType.File
      case ContentItemType.Link:
        return MaterialType.Link
      case ContentItemType.Poster:
        return MaterialType.Poster
      case ContentItemType.Channels:
        return MaterialType.Channels
      case ContentItemType.Text:
        return MaterialType.Text
      case ContentItemType.Media:
        switch (item.media_type) {
          case ContentItemMediaType.Image:
            return MaterialType.Image
          case ContentItemMediaType.Audio:
            return MaterialType.Audio
          case ContentItemMediaType.Video:
            return MaterialType.Video
          case ContentItemMediaType.Voice:
            return MaterialType.Voice
        }
    }

    return MaterialType.Text
  }
}