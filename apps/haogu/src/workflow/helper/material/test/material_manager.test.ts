import { MaterialManager } from '../material_manager'
import { commonMessageSender } from '../../../../config/instance/send_message_instance'
import { Config } from 'config'


describe('materialManagerTest', () => {

  it('getMaterial', async () => {
    const res = await new MaterialManager().searchMaterialFromHaoGu(13, 4)

    console.log(res)
  }, 9e8)

  it('saveMaterial', async () => {
    const res = await new MaterialManager().searchMaterialFromHaoGu(13, 4)


    if (res) {
      console.log(res)
      await new MaterialManager().saveMaterial(res.records[0])
    }


  }, 9e8)

  it('sendMaterial', async () => {
    Config.setting.localTest = false
    await commonMessageSender.sendMaterial('242829731708929028_113', { sourceId:'166'  }, { force:true })
  }, 9e8)

})