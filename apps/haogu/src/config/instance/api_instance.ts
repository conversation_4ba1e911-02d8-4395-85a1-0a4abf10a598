import { Haogu<PERSON><PERSON> } from 'model/haogu/crm/client'
import HaoGuLiveAPI from 'model/haogu/live/client'

const baseUrl = 'https://ddm-test.integrity.com.cn/v1/api/ke'
const appId = '7k3p9x2d5s8f1v6j'
const appSecret = 'KjRoLgkNCXxOCJBdjy3SuuK16i5gPi86mVauKWTvnJU='

export const haoguLiveAPI = new HaoGuLiveAPI(baseUrl, appId, appSecret)

// const haoguScrmBaseUrl = 'http://172.16.14.66:54001'
const haoguScrmBaseUrl = 'http://47.97.123.214:54001'
const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChQjH+wQhar1449U4kMJv9TkHJMd2QyDlSXRMl6r9vLA1uMljtktfGiiuoaP6Y4BJKDnh10IhQOrz8eC2d1I7iRRqEIZXm/88rWyI6UX1IeZ9aFL/VZlensT9qH8dW062hMzApvfGtXCHVmLIL++WAXb/1+MzWRQ4oTgF7WuU1KwIDAQAB'

export const haoguScrmApi = new HaoguApi(haoguScrmBaseUrl, publicKey)
