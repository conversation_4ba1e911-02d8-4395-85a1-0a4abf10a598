import dayjs from 'dayjs'
import logger from 'model/logger/logger'
import {
  ActionInfo,
  GenericStage,
  registerMetaActions,
  registerThinkPrompt,
  StageFilter,
  StageSpec
} from 'service/agent/stage'
import { catchError } from 'lib/error/catchError'
import { chatDBClient, chatStateStoreClient } from '../../service/base_instance'
import { Config } from 'config/'
import { DateHelper } from 'lib/date/date'
import { DataService } from '../../helper/getter/get_data'
import { getUserId } from 'config/chat_id'
import { HumanTransfer } from '../../human_transfer/human_transfer'
import { IChattingFlag } from '../../state/user_flags'
import { JuziAPI } from 'model/juzi/api'
import { MetaActions, ThinkPrompt } from './context'
import { PostAction } from './post_action'
import { HumanTransferType } from 'service/human_transfer/human_transfer'

// ---- Stage-specific helpers & actions ----
async function sendCourseReplay(chat_id: string): Promise<ActionInfo> {
  const course1Backup = (await DataService.getCourseLink(chat_id, 1)) ?? ''
  return { guidance: `给客户发送课程回放${course1Backup}` }
}

async function getGuidance(chatId: string): Promise<ActionInfo> {
  const dayDiff = await PostAction.getNewCourseStartDayDiff(chatId)
  const date = DateHelper.add(new Date(), dayDiff, 'day')
  return { guidance: `可供延期/复训的时间：${DateHelper.getFormattedDate(date, false)}` }
}

async function exitPostpone(chatId: string): Promise<ActionInfo> {
  logger.trace({ chat_id: chatId }, '客户退出延期')
  await chatStateStoreClient.update(chatId, { state: <IChattingFlag>{ is_in_postpone: false } })
  // 重启sop
  await chatDBClient.setStopGroupPush(chatId, false)
  return { guidance: '' }
}

async function executePostpone(chatId: string): Promise<ActionInfo> {
  logger.trace({ chat_id: chatId }, '客户确认延期')
  const userId = getUserId(chatId)
  const oldCourseNo = await DataService.getCourseNoByChatId(chatId)
  await chatStateStoreClient.update(chatId, { state: <IChattingFlag>{ is_in_postpone: false } })
  const currentTime = await DataService.getCurrentTime(chatId)
  if (currentTime.day < 5) {  // 延期
    await chatStateStoreClient.update(chatId, { state: <IChattingFlag>{ has_postpone: true } })
  } else {  // 复训
    await chatStateStoreClient.update(chatId, { state: <IChattingFlag>{ has_refresher_training: true } })
  }
  // 重启sop
  await chatDBClient.setStopGroupPush(chatId, false)
  const dayDiff = (await PostAction.getNewCourseStartDayDiff(chatId)) - 1
  const date = DateHelper.add(new Date(), dayDiff, 'day')
  const newCourseNo = dayjs(date).format('YYYYMMDD')
  await DataService.delayCourseNo(chatId, Number(newCourseNo))

  if (currentTime.day < 5) {
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.ExecutePostpone, 'onlyNotify', `新期数：${newCourseNo}`)
    await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `${oldCourseNo}延期${newCourseNo}`))
  } else {
    await HumanTransfer.transfer(chatId, userId, HumanTransferType.ExecuteRefresherTraining, 'onlyNotify', `新期数：${newCourseNo}`)
    await catchError(JuziAPI.updateUserAlias(Config.setting?.wechatConfig?.id as string, userId, `${oldCourseNo}复训${newCourseNo}`))
  }
  return { guidance: '' }
}

// ---- Stage registry (declarative) ----
registerMetaActions(MetaActions)
registerThinkPrompt(ThinkPrompt)
const STAGES: StageSpec[] = [
  {
    id: 'afterPaid',
    isActive: async (chatId) => await DataService.isPaidSystemCourse(chatId),
  },
  {
    id: 'coursePostpone',
    isActive: async (chatId) => { return (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).is_in_postpone ?? false },
    actions: {
      '回答问题': getGuidance,
      '执行延期': executePostpone,
      '退出延期': exitPostpone,
      '澄清课程时间': getGuidance,
    },
  },
  {
    id: 'duringCourse',
    isActive: async (chatId) => {
      return await DataService.isInCourseTimeLine(chatId, 'inCourse') && !await DataService.isInCourseTimeLine(chatId, 'afterSales')
    },
  },
  {
    id: 'afterCourse3',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterSales', 3),
    actions: {
      '强推陪跑营服务': PostAction.sendCourseIntro,
      '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '提供定金方案': PostAction.provideDepositPlan,
      '保留名额': PostAction.reaskAnotherDay,
    },
  },
  {
    id: 'afterCourse2',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterSales', 2),
    actions: {
      '发送成功案例': PostAction.sendCaseImage,
      '发起购买邀约': PostAction.sendInvitation,
      '保留名额': PostAction.reaskAnotherDay,
    },
  },
  {
    id: 'afterCourse1',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'afterSales', 1),
    actions: async () => ({
      '发送成功案例': PostAction.sendCaseImage,
      '提供延期方案': PostAction.enterPostpone,
      '发送回放': sendCourseReplay,
    }),
  },
  {
    id: 'afterBonding',
    isActive: async (chatId) => {
      const messageCount = await chatStateStoreClient.getNodeCount(chatId, 'UserMessage')
      return await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1) && messageCount > 6 && (await chatStateStoreClient.getFlags<IChattingFlag>(chatId)).after_bonding as boolean
    },
    actions: {
      '发送成功案例': PostAction.sendCaseImage,
      '提供延期方案': PostAction.enterPostpone,
    },
  },
  {
    id: 'afterAdding',
    isActive: async (chatId) => await DataService.isInCourseTimeLine(chatId, 'beforeCourse', 1),
    actions: {
      '进行通用诊断': PostAction.generalDiagnosis,
      '提供延期方案': PostAction.enterPostpone,
    },
  },
]

// ---- Router wiring (order matters: strictly reverse chronological flow) ----
export const stageFilter = new StageFilter(
  STAGES.map((spec) => new GenericStage(spec))
) // 必须严格按照流程倒序添加