import { chatStateStoreClient } from '../../service/base_instance'
import { DataService } from '../../helper/getter/get_data'
import { stageFilter } from './stage'
import { PostAction } from './post_action'
import { PrismaMongoClient } from '../../database/prisma'

describe('metaActionTest', () => {

  it('testGetMetaAction', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 2,
        time: '23:38:00',
      }
    }
    DataService.isPaidSystemCourse = async () => {
      return true
    }
    const chat_id = '7881299950922845_1688858213716953'
    const metaActionStage = await stageFilter.getStageInfo(chat_id)
    console.log(metaActionStage.name)
    console.log(metaActionStage.thinkPrompt)
    console.log(metaActionStage.metaActions)
  }, 9e8)

  it('测试MetaAction', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 4,
        time: '23:38:00',
      }
    }
    DataService.isPaidSystemCourse = async () => {
      return false
    }
    const metaActionStage = await stageFilter.getStageInfo('7881299950922845_1688858213716953')
    console.log(metaActionStage.thinkPrompt)
    console.log(metaActionStage.metaActions)
    console.log(await stageFilter.handleAction('chatId', 'roundId', ['强推陪跑营服务']))
  }, 9e8)

  it('刷新延期状态', async () => {
    const expData = await PrismaMongoClient.getInstance().chat.findMany({
      where: {
        course_no_ori: { isSet: true }
      }
    })

    // const expData = await PrismaMongoClient.getInstance().chat.findMany({
    //   where: {
    //     id: '7881300516060552_1688857404698934'
    //   }
    // })

    for (const item of expData) {
      console.log(item.id)
      const chatState = await chatStateStoreClient.get(item.id)
      chatState.state.has_postpone = true
      await PrismaMongoClient.getInstance().chat.update({
        where: {
          id: item.id
        },
        data: {
          chat_state: chatState
        }
      })
    }
  }, 9e8)

  it('TestGetNewCourseStartDayDiff', async () => {
    DataService.getCurrentTime = async () => {
      return {
        day: 13,
        time: '23:38:00',
      }
    }
    console.log(await PostAction.getNewCourseStartDayDiff('7881299950922845_1688858213716953'))
  }, 60000)
})