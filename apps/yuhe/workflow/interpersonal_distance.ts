import { Runnable } from '@langchain/core/runnables'
import * as hub from 'langchain/hub/node'
import { IWorkflowState } from 'service/llm/state'
import { ContextBuilder } from './context'
import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import { IEventType } from 'model/logger/data_driven'
import logger from 'model/logger/logger'
import { chatHistoryServiceClient } from '../service/base_instance'
import { eventTrackClient } from '../service/event_track_instance'

export class InterpersonalDistance {
  private static _interpersonalDistancePrompt: Runnable | null = null
  private static async getInterpersonalDistancePrompt(): Promise<Runnable> {
    if (!this._interpersonalDistancePrompt) { this._interpersonalDistancePrompt = await hub.pull('interpersonal-distance') }
    return this._interpersonalDistancePrompt
  }

  public static async invoke(state: IWorkflowState) {
    const customerPortrait = await new ContextBuilder({ state }).customerPortrait(state.chat_id)
    const chatHistory = await chatHistoryServiceClient.getChatHistory(state.chat_id, 10, 18)
    const distancePrompt = await this.getInterpersonalDistancePrompt()
    await state.interruptHandler.interruptCheck()
    let result = await LLM.predict(
      distancePrompt, {
        meta: {
          promptName: 'interpersonal_distance',
          chat_id: state.chat_id,
          round_id: state.round_id
        } }, {
        customerInfo: customerPortrait,
        chatHistory: chatHistory,
      })

    if (!result.includes('</distance>')) { result += '</distance>' }
    const distance = XMLHelper.extractContent(result, 'distance') || ''
    eventTrackClient.track(state.chat_id, IEventType.InterpersonalDistance, { round_id: state.round_id, distance: distance })
    logger.debug({ chat_id: state.chat_id, round_id: state.round_id }, `距离感感知结果: ${distance}`)
    return distance
  }
}