import { DataService } from '../getter/get_data'
import ElasticSearchService, { IElasticEmbeddingRes } from 'model/elastic_search/elastic_search'
import logger from 'model/logger/logger'
import { LLM } from 'lib/ai/llm/llm_model'
import { XMLHelper } from 'lib/xml/xml'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { DateHelper } from 'lib/date/date'
import { SystemMessagePromptTemplate } from '@langchain/core/prompts'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'
import { RAGHelper } from 'service/rag/rag_helper'


export class RAG {
  public static index = 'yuhe_general_rag'
  public static nonRAGMatchIndex = 'moer_non_rag_queries_2048d'

  public static async search(inputQuery: string, chat_id: string, round_id: string) {
    inputQuery = await this.mergeQuery(inputQuery, chat_id)
    const isNeedRAG = await this.isNeedRAG(inputQuery)
    if (!isNeedRAG) {
      logger.trace('当前问题无需rag:', inputQuery)
      return ''
    }

    const topic = await UserSlots.getUserSlotSubTopicContent(chatStateStoreClient, chat_id, '基本信息', '行业类目')
    const subQueries = await this.queryReWriteWithChatHistory(inputQuery, topic, chat_id, round_id)
    const embeddingResults = await Promise.all(subQueries.map((subQuery) =>  this.embeddingSearch(chat_id, subQuery)))

    // const filteredResult = this.filterEmbeddingResultByTopic(topic, embeddingResults.flat())
    const retrievedQAs = RAGHelper.removeDuplicateQA(embeddingResults.flat()
      .map((item) => { return { q: item.pageContent, a: item.metadata.a } }))

    const reRankResults = await RAGHelper.reRank(subQueries[1], retrievedQAs, { top_n: 5, threshold: 0.1 })
    logger.trace('reRank Results:', JSON.stringify(reRankResults, null, 2))
    let ragContext = reRankResults.map((qa) => `- 问题：${qa.q.trim()}\n  - 答案：${qa.a.trim()}`).join('\n')

    if (/.*?_\w{4}\.\w+/.test(ragContext)) { // RAG 包含文件
      ragContext = `下面答案中如果提到文件资源为 [文件名称] 格式，请严格按照原始文件名称输出，不要添加额外的文件或信息，并且只参考补充信息中提到的文件资源
${ragContext}`
    }
    return ragContext

  }

  private static async embeddingSearch(chat_id: string, query: string): Promise<IElasticEmbeddingRes[]> {
    // 获取要查询的文档
    const queryDocs = await this.getQueryDocs(chat_id)

    // 构建查询 filter
    const filter = {
      terms: {
        'metadata.doc': queryDocs
      }
    }

    // Embedding Search
    return await ElasticSearchService.embeddingSearch(
      this.index,
      query,
      10,
      0.74,
      filter
    )
  }


  private static async getQueryDocs(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)

    const commonDoc = '常规问题全局'
    const beforeSalesDoc = '售前问题'
    const day1CourseDoc = '第一天课程逐字稿'
    const day2CourseDoc = '第二天课程逐字稿'
    const day3CourseDoc = '第三天课程逐字稿'
    const day4CourseDoc = '第四天课程逐字稿'
    const salesDoc = '销售问题'
    const afterSalesDoc = '售后问题'


    const preCourseDoc = [
      commonDoc,
      beforeSalesDoc
    ]
    const day1Doc = [
      commonDoc,
      day1CourseDoc,
      beforeSalesDoc
    ]
    const day2Doc = [
      commonDoc,
      day1CourseDoc,
      day2CourseDoc,
      salesDoc
    ]
    const day3Doc = [
      commonDoc,
      day1CourseDoc,
      day2CourseDoc,
      day3CourseDoc,
      salesDoc
    ]
    const day4Doc = [
      commonDoc,
      day1CourseDoc,
      day2CourseDoc,
      day3CourseDoc,
      day4CourseDoc,
      salesDoc
    ]

    let resDoc: string[] = []

    if (currentTime.post_course_day) {
      resDoc = day4Doc
    }

    if (await this.isBeforeFirstCourse(chat_id)) {
      resDoc = preCourseDoc
    } else if (await this.isBeforeSecondCourse(chat_id))
    {
      resDoc = day1Doc
    } else if (await this.isBeforeThirdCourse(chat_id))
    {
      resDoc = day2Doc
    }  else if (await this.isBeforeFourthCourse(chat_id))
    {
      resDoc = day3Doc
    }

    if (await DataService.isPaidSystemCourse(chat_id)) {
      resDoc.push(afterSalesDoc)
    }

    return resDoc
  }

  private static async mergeQuery(inputQuery: string, chat_id: string) {
    const chatHistory = await chatHistoryServiceClient.getChatHistoryByChatId(chat_id)

    for (let i = chatHistory.length - 2; i >= 0; i--) {
      if (chatHistory[i].role === 'user')
      {
        inputQuery = `${chatHistory[i].content}\n${inputQuery}`
      } else {
        return inputQuery
      }
    }

    return inputQuery
  }

  private static async isNeedRAG(inputQuery: string) {
    const queryParts = inputQuery.split(/[，。\s]+/).filter((part) => part.trim() !== '')
    const unmatchedParts: string[] = []

    for (const part of queryParts) {
      const ragResults = await ElasticSearchService.embeddingSearch(this.nonRAGMatchIndex, part, 1, 0.8)

      if (ragResults.length === 0) {
        // 没有搜索到了，输出，后面要rag
        unmatchedParts.push(part)
      } else {
        logger.trace(`被删除的部分query "${part}":`, ragResults)
      }
    }

    return unmatchedParts.length > 0 && RAGHelper.isNeedRAG(unmatchedParts.join())
  }

  private static filterEmbeddingResultByTopic(topic: string, embeddingResults: IElasticEmbeddingRes[]) {
    if (topic === '') {
      return embeddingResults
    }

    const res: IElasticEmbeddingRes[] = []
    for (const embeddingResult of embeddingResults) {
      if (!embeddingResult.metadata.tag || embeddingResult.metadata.tag === '' || embeddingResult.metadata.tag === topic) {
        res.push(embeddingResult)
      }
    }
    return res
  }

  private static async queryReWriteWithChatHistory(
    query: string,
    topic: string,
    chat_id: string,
    round_id: string
  ): Promise<string[]> {
    try {
      const chatHistory = await chatHistoryServiceClient.getChatHistory(chat_id, 3, 6)

      const promptTemplate = SystemMessagePromptTemplate.fromTemplate(`# 意图改写
您的任务是为搜索引擎提供一个更合适的意图改写，以回答给定的问题。请将每个推断的问题简洁地用<query></query>标签括起来

## 规则限制
- 优先根据客户的原始问题生成推断问题，不偏离其语义。必要时，可以使用上下文来细化或增强问题，但不得改变原问题的核心意图
- 仅在客户问题过于模糊时，使用上下文来补充和澄清问题。如果对话记录与客户查询无关，请专注于客户查询，不要插入对话记录信息中的无关内容
- 从聊天记录中识别出主要的主题、话题或问题
- 在所有推断问题中避免使用代词或模糊地引用（例如：“这个”“那个”“这些”“那些”）。始终用具体、明确的主题或问题替换
- 生成 1 个有洞察力的改写查询，并放在标签<query></query>中
- 用中文回答，只需要输出结果（用标签<query></query>包括的改写查询），不要输出其他内容

## 对话历史
{chatHistory}

## 客户行业
{topic}

## 客户发言
{query}

开始输出`)
      const llm = new LLM({
        meta: {
          promptName: 'query_rewrite',
          chat_id: chat_id,
          round_id: round_id,
        },
      }) // 添加日志，方便追踪
      const extractedRawQuery = await llm.predict(promptTemplate, { chatHistory, topic, query })

      const subQueries = XMLHelper.extractContents(extractedRawQuery, 'query')
      if (!subQueries) {
        return [query]
      }

      return [query, ...subQueries]
    } catch (e) {
      logger.error('Error in query rewrite:', e)
      return [query]
    }
  }

  private static async isBeforeFirstCourse(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    return (
      currentTime.day < 1 ||
      (currentTime.day === 1 &&
        DateHelper.isTimeBefore(currentTime.time, '21:42:00'))
    )
  }

  private static async isBeforeSecondCourse(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    return (
      currentTime.day < 2 ||
      (currentTime.day === 2 &&
        DateHelper.isTimeBefore(currentTime.time, '20:43:00'))
    )
  }

  private static async isBeforeThirdCourse(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    return (
      currentTime.day < 3 ||
      (currentTime.day === 3 &&
        DateHelper.isTimeBefore(currentTime.time, '20:20:00'))
    )
  }

  private static async isBeforeFourthCourse(chat_id: string) {
    const currentTime = await DataService.getCurrentTime(chat_id)
    return (
      currentTime.day < 4 ||
      (currentTime.day === 4 &&
        DateHelper.isTimeBefore(currentTime.time, '22:00:00'))
    )
  }
}