import logger from 'model/logger/logger'
import { accountToName } from '../config/config'
import { DataService } from '../helper/getter/get_data'
import { eventTrackClient } from '../service/event_track_instance'
import { humanTransferClient } from '../service/human_transfer_instance'
import { IEventType } from 'model/logger/data_driven'
import { HumanTransferType } from 'service/human_transfer/human_transfer'

/**
 * 宇和项目转人工处理
 */
export class HumanTransfer {
  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param transferMessage
   * @param toHuman
   * @param additionalMsg
   */
  public static async transfer(chatId: string, userId: string, transferMessage: string, toHuman: boolean | 'onlyNotify' = true, additionalMsg?: string) {
    if (userId === 'null') {
      logger.error('[YuHeHumanTransfer] userId is null', transferMessage)
      return
    }
    if (transferMessage !== HumanTransferType.UnknownMessageType)  { // 因为图片，文件等转人工的，日志在上级进行处理，这里不进行重复处理
      eventTrackClient.track(chatId, IEventType.TransferToManual, { reason: transferMessage })
    }
    // 拼接要发送的消息
    const ta_id = chatId.split('_')[1]
    const courseNo = await DataService.getCourseNoByChatId(chatId) ?? 2025
    const accountName = accountToName[ta_id] ?? '未知'
    const ip = await DataService.getIpByChatId(chatId)
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'
    const message = `（${ ip }${ courseNo }${ accountName }）${ transferMessage }，${ handleType }${ additionalMsg ? `\n${ additionalMsg }` : ''}`
    return await humanTransferClient.transfer(chatId, userId, message, toHuman)
  }
}