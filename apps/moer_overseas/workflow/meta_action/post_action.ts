import { DataService } from '../../helper/getter/get_data'
import { LLM } from 'lib/ai/llm/llm_model'
import { AIMessage, HumanMessage, SystemMessage } from '@langchain/core/messages'
import { XMLHelper } from 'lib/xml/xml'
import { UserSlots } from 'service/user_slots/extract_user_slots'
import { ActionInfo } from 'service/agent/stage'
import { chatHistoryServiceClient, chatStateStoreClient } from '../../service/base_instance'

export class PostAction {

  public static async sendInvitation(): Promise<ActionInfo> {
    // actionInfo.callback = async () => {
    //   await PostAction.sendInstallmentVideo(chat_id, user_id)
    // }
    return { guidance: '下单链接:https://m.ztnzen.com/pages/course/detail?sku=20250624002723' }
  }

  public static async sendCaseImage(chat_id: string, round_id:  string): Promise<ActionInfo> {
    // const imageStr = await PostAction.getSalesCase(chat_id, round_id)
    // if (!imageStr) {
    //   return { guidance: '' }
    // }
    // eventTrackClient.track(chat_id, IEventType.salesCase, { round_id: round_id, case: imageStr })
    //
    // // 最多发送2张图片
    // const images = imageStr.split('，').slice(0, 2)
    //
    // const imageSearchResult = await Promise.all(images.map(async (image) => {
    //   const resultList = await MoreImageGeneral.searchSalesCaseImage(image, ['sales_case'], 1)
    //   return resultList[0] ?? { description: '', url: '' }
    // }))
    //
    // const imagesShouldSend = imageSearchResult.filter(async (image) => image.url !== '' && !await chatHistoryServiceClient.isRepeatedMsg(chat_id, image.description))
    //
    // if (imagesShouldSend.length > 0) {
    //   //发送图片函数
    //   const sendImages = async () => {
    //     for (const image of imagesShouldSend) {
    //       await sleep(2000)
    //       await yCloudMessageSender.sendById({
    //         chat_id: chat_id,
    //         ai_msg: image.description,
    //         type: 'image',
    //         send_msg: <WhatsappMessageMedia>{ link: image.url }
    //       })
    //     }
    //   }
    //   const guidance = `案例图片信息：${imagesShouldSend.map((image) => image.description).join('\n')}`
    //   return { guidance: guidance, callback: sendImages }
    // }
    return { guidance: '' }
  }

  public static async sendPreCourseLink(chat_id: string): Promise<ActionInfo> {
    const preCourseLink = await DataService.getCourseLink(0, chat_id)
    return { guidance: `小讲堂链接：${preCourseLink}` }
  }

  public static async getSalesCase(chat_id: string, round_id: string) {
    const userPortrait = UserSlots.fromRecord((await chatStateStoreClient.get(chat_id)).userSlots ?? {}).toString()
    const chatHistory = await chatHistoryServiceClient.getChatHistory(chat_id, 3, 12)
    const result = await LLM.predictMessage([
      new SystemMessage(`# 销售案例
你是销售天才，正在给客户销售冥想课，你擅长用历史案例推动销售进程，请根据客户主要痛点与下单卡点，结合近几轮的对话，判断是否需要使用历史案例来辅助销售

## 历史案例
- 克服恐惧_找到内心平静，冥想感受很好_学费花的太值了，办事效率提升_身体变好_学会盘腿，助教老师细心负责_身心得到成长，学习一个月后_能量状态飙升，学会呼吸_呼吸变长_希望能够帮助更多的人，帮助平静内心_调整身体的不适，很少头疼了，情绪减压释放_走向光明觉醒，意念改变影响财富显化_学会双盘了，成长精进满满_60岁还在学习收获很大，提升专注力_做事保持专注，提升专注力_做事效率变高，提升觉知后_改善健康，缓解压力_财富增长，改变自己看法的_走出抑郁_情绪管理_身体健康，焦虑减少_在助教陪伴下坚持学习冥想，状态好了_财富也开始显化了，班主任的服务负责，睡眠质量提升，系统班班班用心负责_及时跟进每个人学习进度和反馈，系统班课程大纲，系统班课程性价比很高，练习几天就能量飙升，练习火呼吸疗愈身体的健康，能量提升后_克服恐惧，能量提升后_缓解工作焦虑，认知提升_情绪减压释放，重度抑郁焦虑_通过冥想克服恐惧实现改变

## 要求
- 请先在 <think></think> 标签中结合客户下单卡点，对话记录分析需要用什么历史案例可以打动客户，请在精准匹配客户的情况下使用，不要过度推理，不要重复使用已经出现在对话记录中发送过的案例
- 然后在 <image></image> 标签中直接输出历史案例，没有参考案例或者没有匹配上可不输出，最多不超过2个案例，用“，”隔开
- 只有必要的时候才发送案例，如果当前的聊天场景不适合发送案例，就不要输出任何案例
- 你需要根据最后两句麦子老师的发言，判断是否需要案例来佐证观点，不要发送牵强的案例
- 对话记录中用[]包括的为发送的案例，如果历史中发过案例，避免频繁发送`),
      new HumanMessage(`## 对话历史
麦子老师: [day4未到课提醒 20:10]
麦子老师: 点击链接进入直播课堂：https://t.meihao.com/snsz
麦子老师: 同学咱这边掉线了，是有什么问题吗? 第三课：唐宁老师会传授只有线下才会开设的蓝鹰预演，帮助我们更好地掌控自己的情绪和思维，认识自己、明确目标，并激发内在的力量，提高振动频率，以实现梦想，也能更从容地应对新挑战与困难。同时还会学习无限数息法延长呼吸，如何深呼吸延长几倍
客户: 线上课怎么这么贵
1880呀`),
      new AIMessage(`<think>客户的主要痛点在于课程价格较高，需要用能够体现课程价值和性价比的案例来打动客户，比如系统班课程性价比很高，冥想感受很好_学费花的太值了</think>
<image>系统班课程性价比很高，冥想感受很好_学费花的太值了</image>`),
      new HumanMessage(`## 对话历史
麦子老师: 加油继续冥想练习，未来一定会有更多的收获！
麦子老师: 祝福你呀❤️
麦子老师: [唐宁老师加油表情包]
客户: 收到，感恩有您！`),
      new AIMessage(`<think>客户目前对课程表现出感恩的态度，但并未提到具体的痛点或下单卡点，没有明确需要使用历史案例来辅助销售。</think>
<image></image>`),
      new HumanMessage(`## 对话历史
客户: 你还认为我不相信冥想，你还不知道我的顾虑在哪里啊？
麦子老师: 哈哈，看来我确实还没完全get到你的点！ 不过没关系，你的顾虑我一定会认真倾听和理解
麦子老师: 是不是关于课程的效果，或者担心自己能不能坚持学下来？ 还是别的什么呢？
麦子老师: 你直接告诉我，我一定帮你分析清楚！
麦子老师: [认知提升_情绪减压释放]
麦子老师: [状态好了_财富也开始显化了]
麦子老师: 给您看看我们学员参加21天系统课后的真实反馈，或许能帮您更好地了解课程的效果！
客户: 如果我不相信冥想，从开始到现在我一直都在谈冥想给带来心中一点的变化，而且你不提、我还主动以冥想效应产生为话题，你没觉察出来吗？
麦子老师: 哎呀，同学，你这么一说，我确实反应慢了些！ 你从头到尾的分享，其实已经表现出对冥想的认可了，是我刚刚没抓住重点，抱歉哈😅
麦子老师: 既然冥想带给你心中的变化是肯定的，那我猜你的顾虑可能更多是关于课程本身——比如，它是否真的能解决像腰痛、情绪波动这些具体问题？ 或者是不是担心能不能坚持学完？
麦子老师: 如果我猜错了，你可以直接告诉我哦，我一定好好帮你分析~`),
      new AIMessage(`<think>麦子老师在前几轮对话中发送过案例[认知提升_情绪减压释放][状态好了_财富也开始显化了]，为避免频繁发送影响客户，本次无需发送案例。</think>
<image></image>`),
      new HumanMessage(`${userPortrait}

## 对话历史
${chatHistory}`),
    ], { meta: { chat_id: chat_id, promptName: 'send_sales_case', round_id: round_id } })
    // if (!fileName) {
    //   return '其他资料'
    // }
    return XMLHelper.extractContent(result, 'image') || ''
  }
}