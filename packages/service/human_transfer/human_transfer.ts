import logger from 'model/logger/logger'
import { ChatDB, IChat } from '../database/chat'
import { ChatStateStore } from '../local_cache/chat_state_store'
import { Config } from 'config'
import { GroupNotification } from '../group_notification/group_notification'
import { EventTracker, IEventType } from 'model/logger/data_driven'
import { JuziAPI } from 'model/juzi/api'

export const HumanTransferType = {
  UnknownMessageType: '客户发了一个文件',
  NotBindPhone: '客户手机号绑定失败',
  ProblemSolving: '客户遇到问题',
  FailedToJoinGroup: '客户拉群失败',
  ProcessImage: '客户发了一张【图片】',
  MessageSendFailed: '消息发送失败',
  ReaskAnotherDay: '客户次日被主动提醒支付',
  HesitatePayment: '客户支付犹豫',
  LogOutNotify: '客户直播掉线',
  ExecutePostpone: '客户已延期',
  VoiceOrVideoCall: '客户发起语音/视频通话',
  SoftwareIssue: '客户软件或者课程链接出问题',
  RobotDetected: '客户识别到了AI',
  PaidCourse: '[烟花]客户已支付[烟花]',
  ProcessVideo: '客户发了一个【视频】',
  ProcessVideoFailed: '客户发了一个【视频】，识别失败',
  ExplicitlyPurchases: '客户弹幕识别出支付意向',
  ExecuteRefresherTraining: '客户已复训',
} as const

export class BaseHumanTransfer {
  chatDBClient:ChatDB<IChat>
  chatStateStoreClient:ChatStateStore

  constructor(chatDBClient:ChatDB<IChat>, chatStateStoreClient:ChatStateStore) {
    this.chatDBClient = chatDBClient
    this.chatStateStoreClient = chatStateStoreClient
  }

  /**
   * 转交人工，toBot为true时，表示转交机器人
   * @param chatId
   * @param userId
   * @param notifyMessage
   * @param toHuman
   * @param imBotId
   */

  public async transfer(chatId: string, userId: string, notifyMessage: string, toHuman: boolean |'onlyNotify' = true, imBotId?: string) {
    const chat = await this.chatDBClient.getById(chatId) as IChat
    if (typeof toHuman === 'boolean' && chat) {
      await this.chatDBClient.setHumanInvolvement(chatId, toHuman)
    } else {
      if (!chat) {
        const currentSender = await JuziAPI.getCustomerInfo(Config.setting.wechatConfig?.id as string, userId)
        await this.chatDBClient.create({
          id: chatId,
          round_ids: [],
          contact: {
            wx_id: userId,
            wx_name: currentSender ? currentSender.name : userId,
          },
          wx_id: Config.setting.wechatConfig?.id as string,
          created_at: new Date(),
          chat_state: await this.chatStateStoreClient.get(chatId)
        })
      }
    }

    // 包装通知
    let contactName = userId
    let notificationMessage: string
    const isPaid = chat.chat_state.state.is_complete_payment ?? false
    if (chat && chat.contact && chat.contact.wx_name) {
      contactName = chat.contact.wx_name
    }
    notificationMessage = `${contactName} ${isPaid ? '💰' : ''}${notifyMessage}`

    if (toHuman === true) { notificationMessage += '\n[心碎]AI已关闭[心碎]' }
    logger.log({ chat_id: chatId }, '通知人工接入：', notificationMessage)
    if (toHuman === 'onlyNotify' && Config.setting.localTest) return

    await GroupNotification.notify(notificationMessage,  imBotId) // 群通知
  }
}

export type TransferKey = string | number

export interface HumanTransferConfig<T extends TransferKey> {
    transferMessage
    eventTracker: EventTracker
    humanTransferClient: BaseHumanTransfer
}

export type TransferMode = true | false | 'onlyNotify'


export class HumanTransfer<T extends TransferKey> {
  private cfg: HumanTransferConfig<T>
  constructor(cfg: HumanTransferConfig<T>) {
    this.cfg = cfg
  }

  public async transfer(
    chatId: string,
    userId: string,
    transferType: T,
    toHuman: TransferMode = true,
    additionalMsg?: string
  ) {
    if (userId === 'null') {
      logger.error('[HumanTransfer] userId is null', transferType)
      return
    }

    this.cfg.eventTracker.track(chatId, IEventType.TransferToManual, { reason: this.cfg.transferMessage[transferType] })

    // 拼接要发送的消息
    // const ta_id = getBotId(chatId)
    const handleType = toHuman === true ? '请人工处理' : '请观察👀'

    const message = `${ this.cfg.transferMessage[transferType] as string }，${ handleType }${ additionalMsg ? `\n${additionalMsg}` : ''}`
    return await this.cfg.humanTransferClient.transfer(chatId, userId, message, toHuman)
  }
}